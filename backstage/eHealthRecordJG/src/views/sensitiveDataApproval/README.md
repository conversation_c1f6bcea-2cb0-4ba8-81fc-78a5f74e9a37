# 敏感数据上报与审批页面

## 页面概述
这是一个用于管理敏感数据上报与审批流程的页面，主要功能包括：
- 查看数据上报列表
- 筛选和搜索上报记录
- 查看审批流程详情（右侧抽屉）
- 执行审批操作（审批弹窗）
- 跟踪审批状态

## 页面结构

### 1. 查询条件区域
- **劳动者姓名**：支持模糊搜索
- **审批状态**：下拉选择（待审批、审批中、已通过、已拒绝）
- **上报时间**：日期范围选择器
- **查询按钮**：执行搜索
- **重置按钮**：清空搜索条件

### 2. 数据列表区域
包含以下字段：
- **序号**：自动编号
- **劳动者姓名**：上报涉及的劳动者
- **审批状态**：当前审批状态（带颜色标签）
- **上报理由**：详细的上报原因说明
- **当前审批节点**：显示当前处于哪个审批环节
- **上报时间**：数据上报的时间
- **上报单位**：提交上报的企业名称
- **操作**：包含两个按钮
  - **查看流程**：打开右侧抽屉查看详细流程
  - **审批**：仅对待审批和审批中的记录显示，打开审批弹窗

### 3. 审批流程抽屉（右侧）
点击"查看流程"按钮后从右侧滑出：
- **流程概览**：步骤条显示整个审批流程和当前进度
- **审批历史**：详细的审批记录表格
  - 审批节点
  - 审批人
  - 审批时间
  - 审批结果
  - 审批意见

### 4. 审批操作弹窗
点击"审批"按钮后显示：
- **审批信息**：显示当前记录的基本信息
- **审批结果**：单选框（通过/拒绝）
- **审批意见**：必填的文本域，最少5个字符
- **提交按钮**：提交审批结果

## 技术实现

### 组件依赖
- `TitleTag`：标题组件
- Element UI 组件：表格、分页、弹窗、表单等

### API 接口
- `getDataReportApprovalList`：获取上报审批列表
- `getApprovalFlowDetail`：获取审批流程详情
- `submitApprovalResult`：提交审批结果

### 状态管理
- `tableData`：列表数据
- `pageInfo`：分页信息
- `searchData`：搜索条件
- `flowDrawerVisible`：流程抽屉显示状态
- `currentFlowData`：当前查看的流程数据
- `approvalDialogVisible`：审批弹窗显示状态
- `currentApprovalData`：当前审批的记录数据
- `approvalForm`：审批表单数据
- `approvalRules`：审批表单验证规则

## 🔧 功能特点

1. **搜索和筛选**: 支持按姓名、状态、时间范围筛选
2. **分页显示**: 完整的分页功能
3. **状态标签**: 不同审批状态用不同颜色的标签显示
4. **流程可视化**: 使用步骤条展示审批进度
5. **详细历史**: 完整的审批历史记录表格
6. **右侧抽屉**: 流程查看使用抽屉组件，不遮挡主界面
7. **审批操作**: 集成审批功能，支持通过/拒绝操作
8. **表单验证**: 审批意见必填，确保审批质量
9. **权限控制**: 只有待审批和审批中的记录才显示审批按钮
10. **响应式设计**: 适配不同屏幕尺寸

## 样式特点
- 参考 `recordManagement/index.vue` 的布局和样式
- 响应式设计，支持不同屏幕尺寸
- 统一的色彩方案和交互效果
- 清晰的信息层次结构

## 使用说明

### 开发环境
1. 确保已安装项目依赖
2. 将页面文件放置在 `src/views/dataReportApproval/` 目录下
3. 在路由配置中添加对应路由
4. 根据实际后端接口调整 API 调用

### 生产环境
1. 替换模拟数据为真实 API 调用
2. 根据实际业务需求调整字段显示
3. 配置相应的权限控制
4. 添加错误处理和加载状态
5. 配置审批流程节点和权限
6. 集成消息通知功能

## 自定义配置

### 修改搜索条件
在 `searchData` 中添加或修改字段，并在模板中添加对应的表单控件。

### 调整表格列
在 `el-table-column` 中修改列配置，包括宽度、对齐方式、显示内容等。

### 自定义审批状态
在 `getApprovalStatusType` 和 `getApprovalStatusText` 方法中修改状态映射。

## 注意事项
1. 当前使用模拟数据，实际部署时需要连接真实后端接口
2. 审批流程的步骤和节点需要根据实际业务流程调整
3. 权限控制需要根据用户角色进行配置
4. 建议添加数据导出功能以便于数据分析
5. 审批按钮只对待审批和审批中的记录显示，已完成的记录不可再次审批
6. 审批意见为必填项，最少需要5个字符
7. 右侧抽屉可以通过ESC键或点击遮罩层关闭
8. 审批操作提交后会自动刷新列表数据

## 更新日志
- **v1.1**: 将流程查看从弹窗改为右侧抽屉
- **v1.1**: 添加审批功能，支持通过/拒绝操作
- **v1.1**: 增加表单验证和权限控制
- **v1.1**: 优化用户交互体验
