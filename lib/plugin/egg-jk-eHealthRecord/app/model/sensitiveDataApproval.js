module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const SensitiveDataApproval = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },

    // 搜索条件的用人单位名称
    cname: {
      type: String,
      default: '',
    },

    // 搜索条件的检查时间点
    timePoint: {
      type: Date,
      default: '',
    },

    // 搜索条件的检查日期范围
    timeRange: {
      type: Array,
      dafault: [],
    },

    // 搜索条件的地址
    district: {
      type: Array,
      dafault: [],
    },

    // 搜索条件区域码
    districtCode: {
      type: Array,
      dafault: [],
    },

    // 搜索条件的操作人
    operator: {
      type: String,
      default: '',
    },

    // 搜索条件的操作时间点
    operateTime: {
      type: Date,
      default: '',
    },

    // 搜索条件的操作时间范围
    operateRangeTime: {
      type: Array,
      dafault: [],
    },
  }, {
    timestamps: true,
  });

  // 创建索引
  SensitiveDataApproval.index({ cname: 1 });
  SensitiveDataApproval.index({ timePoint: 1 });
  SensitiveDataApproval.index({ district: 1 });
  SensitiveDataApproval.index({ timeRange: 1 });
  SensitiveDataApproval.index({ operator: 1 });
  SensitiveDataApproval.index({ operateTime: 1 });
  SensitiveDataApproval.index({ operateTimeRange: 1 });
  SensitiveDataApproval.index({ districtCode: 1 });
  SensitiveDataApproval.index({ unique: true });

  return mongoose.model('SaveConditions', SensitiveDataApproval);
};
