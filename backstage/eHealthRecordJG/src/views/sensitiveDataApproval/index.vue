<template>
  <div class="padding" style="height:100%">
    <TitleTag :titleName="'查询条件'"> </TitleTag>
    <div class="dataReportApproval">
      <el-row>
        <el-col :span="24">
          <div class="first-line">
            <div class="search-item">
              <span class="label-title">劳动者姓名</span>
              <el-input v-model="searchData.workerName" placeholder="请输入劳动者姓名"></el-input>
            </div>
            
            <div class="search-item">
              <span class="label-title">审批状态</span>
              <el-select v-model="searchData.approvalStatus" placeholder="请选择审批状态" clearable>
                <el-option label="待审批" value="pending"></el-option>
                <el-option label="审批中" value="processing"></el-option>
                <el-option label="已通过" value="approved"></el-option>
                <el-option label="已拒绝" value="rejected"></el-option>
              </el-select>
            </div>

            <div class="search-item">
              <span class="label-title">上报时间</span>
              <el-date-picker 
                v-model="searchData.reportDateRange" 
                type="daterange" 
                placeholder="选择日期" 
                range-separator="至"
                start-placeholder="起始日期" 
                end-placeholder="结束日期">
              </el-date-picker>
            </div>

            <div class="search-item">
              <el-button type="primary" size="mini" style="margin-left: 10px;" @click="search" icon="el-icon-search"
                plain>查询</el-button>
              <el-button type="warning" size="mini" @click="reset" icon="el-icon-refresh-right" plain>重置</el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <TitleTag :titleName="'数据上报审批列表'"> </TitleTag>
    <div class="dataReportApproval">
      <div class="table">
        <el-table :data="tableData" stripe border tooltip-effect="dark"
          :header-cell-style="{ background: '#F5F7FA', fontSize: '14px', fontWeight: 700, color: '#333' }">
          <el-table-column type="index" :index="indexMethod" label="序号" align="center" width="60"></el-table-column>
          <el-table-column prop="workerName" label="劳动者姓名" align="center" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="approvalStatus" label="审批状态" align="center" width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tag :type="getApprovalStatusType(scope.row.approvalStatus)">
                {{ getApprovalStatusText(scope.row.approvalStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="reportReason" label="上报理由" align="center" min-width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="currentApprovalNode" label="当前审批节点" align="center" width="150" show-overflow-tooltip></el-table-column>
          <el-table-column prop="reportTime" label="上报时间" align="center" width="150" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ formatDate(scope.row.reportTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="enterpriseName" label="上报单位" align="center" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" align="center" width="200">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" plain @click="viewApprovalFlow(scope.row)">查看流程</el-button>
              <el-button
                v-if="scope.row.approvalStatus === 'pending' || scope.row.approvalStatus === 'processing'"
                type="success"
                size="mini"
                plain
                @click="openApprovalDialog(scope.row)"
                style="margin-left: 5px;">
                审批
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination @size-change="getData" @current-change="getData" :current-page.sync="pageInfo.pageNum"
          :page-size.sync="pageInfo.pageSize" :page-sizes="[10, 20, 30, 50, 100]" background
          layout="total, sizes, prev, pager, next, jumper" :total="total">
        </el-pagination>
      </div>
    </div>

    <!-- 上报流程右侧抽屉 -->
    <el-drawer
      title="上报审批流程"
      :visible.sync="flowDrawerVisible"
      direction="rtl"
      size="60%"
      :close-on-press-escape="false">
      <div class="flow-content" style="padding: 20px;">
        <div class="flow-header">
          <h4>{{ currentFlowData.workerName }} - 数据上报审批流程</h4>
          <p class="flow-reason">上报理由：{{ currentFlowData.reportReason }}</p>
        </div>
        <div class="flow-steps">
          <el-steps :active="currentFlowData.currentStep" finish-status="success" align-center>
            <el-step
              v-for="(step, index) in currentFlowData.approvalSteps"
              :key="index"
              :title="step.nodeName"
              :description="step.description">
            </el-step>
          </el-steps>
        </div>
        <div class="flow-details">
          <h5 style="margin: 20px 0 10px 0;">审批历史</h5>
          <el-table :data="currentFlowData.approvalHistory" border>
            <el-table-column prop="nodeName" label="审批节点" align="center" width="120"></el-table-column>
            <el-table-column prop="approverName" label="审批人" align="center" width="100"></el-table-column>
            <el-table-column prop="approvalTime" label="审批时间" align="center" width="140">
              <template slot-scope="scope">
                {{ formatDate(scope.row.approvalTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="approvalResult" label="审批结果" align="center" width="80">
              <template slot-scope="scope">
                <el-tag :type="getApprovalResultType(scope.row.approvalResult)" size="mini">
                  {{ scope.row.approvalResult }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="approvalComment" label="审批意见" align="center" min-width="150" show-overflow-tooltip></el-table-column>
          </el-table>
        </div>
      </div>
    </el-drawer>

    <!-- 审批弹窗 -->
    <el-dialog title="审批操作" :visible.sync="approvalDialogVisible" width="600px" :close-on-click-modal="false">
      <div class="approval-content">
        <div class="approval-info">
          <h4>{{ currentApprovalData.workerName }} - 审批信息</h4>
          <p><strong>上报理由：</strong>{{ currentApprovalData.reportReason }}</p>
          <p><strong>当前节点：</strong>{{ currentApprovalData.currentApprovalNode }}</p>
          <p><strong>上报时间：</strong>{{ formatDate(currentApprovalData.reportTime) }}</p>
        </div>

        <el-form :model="approvalForm" :rules="approvalRules" ref="approvalForm" label-width="100px">
          <el-form-item label="审批结果" prop="result">
            <el-radio-group v-model="approvalForm.result">
              <el-radio label="approve">通过</el-radio>
              <el-radio label="reject">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="审批意见" prop="comment">
            <el-input
              v-model="approvalForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入审批意见"
              maxlength="500"
              show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="approvalDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitApproval" :loading="approvalLoading">提交审批</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import { getDataReportApprovalList, getApprovalFlowDetail, submitApprovalResult } from '@/api'

export default {
  components: {
    TitleTag,
  },
  data() {
    return {
      tableData: [],
      pageInfo: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      searchData: {
        workerName: '',
        approvalStatus: '',
        reportDateRange: []
      },
      // 流程抽屉相关
      flowDrawerVisible: false,
      currentFlowData: {
        workerName: '',
        reportReason: '',
        currentStep: 0,
        approvalSteps: [],
        approvalHistory: []
      },
      // 审批弹窗相关
      approvalDialogVisible: false,
      approvalLoading: false,
      currentApprovalData: {
        id: '',
        workerName: '',
        reportReason: '',
        currentApprovalNode: '',
        reportTime: ''
      },
      approvalForm: {
        result: '',
        comment: ''
      },
      approvalRules: {
        result: [
          { required: true, message: '请选择审批结果', trigger: 'change' }
        ],
        comment: [
          { required: true, message: '请输入审批意见', trigger: 'blur' },
          { min: 5, message: '审批意见至少5个字符', trigger: 'blur' }
        ]
      }
    }
  },

  created() {
    this.getData()
  },

  methods: {
    async getData() {
      try {
        const params = {
          ...this.pageInfo,
          ...this.searchData
        };

        // 处理日期范围
        if (this.searchData.reportDateRange && this.searchData.reportDateRange.length === 2) {
          params.startDate = this.searchData.reportDateRange[0];
          params.endDate = this.searchData.reportDateRange[1];
        }
        delete params.reportDateRange;

        // 暂时使用模拟数据，实际使用时取消注释下面的代码
        // const res = await getDataReportApprovalList(params);
        // this.tableData = res.data.list || [];
        // this.total = res.data.total || 0;

        // 模拟数据
        this.tableData = [
          {
            id: '1',
            workerName: '张三',
            approvalStatus: 'pending',
            reportReason: '职业病疑似病例上报，需要进一步确认诊断结果',
            currentApprovalNode: '初审',
            reportTime: '2024-01-15 09:30:00',
            enterpriseName: '某某制造有限公司'
          },
          {
            id: '2',
            workerName: '李四',
            approvalStatus: 'processing',
            reportReason: '工伤事故上报，涉及机械操作安全事故',
            currentApprovalNode: '复审',
            reportTime: '2024-01-14 14:20:00',
            enterpriseName: '某某建筑工程公司'
          },
          {
            id: '3',
            workerName: '王五',
            approvalStatus: 'approved',
            reportReason: '健康档案异常数据上报，血压指标超标',
            currentApprovalNode: '已完成',
            reportTime: '2024-01-13 11:15:00',
            enterpriseName: '某某化工有限公司'
          },
          {
            id: '4',
            workerName: '赵六',
            approvalStatus: 'rejected',
            reportReason: '疑似职业病上报，但缺少相关检查报告',
            currentApprovalNode: '初审',
            reportTime: '2024-01-12 16:45:00',
            enterpriseName: '某某电子科技公司'
          }
        ];
        this.total = 4;
      } catch (error) {
        this.$message.error('获取数据失败');
        console.error(error);
      }
    },

    search() {
      this.pageInfo = this.$options.data()['pageInfo'];
      this.getData();
    },

    reset() {
      this.searchData = this.$options.data()['searchData'];
      this.pageInfo = this.$options.data()['pageInfo'];
      this.getData();
    },

    async viewApprovalFlow(row) {
      try {
        // 暂时使用模拟数据，实际使用时取消注释下面的代码
        // const res = await getApprovalFlowDetail({ id: row.id });

        // 模拟审批流程数据
        const mockFlowData = {
          currentStep: row.approvalStatus === 'pending' ? 0 :
                      row.approvalStatus === 'processing' ? 1 :
                      row.approvalStatus === 'approved' ? 3 : 0,
          approvalSteps: [
            { nodeName: '初审', description: '初步审核上报内容' },
            { nodeName: '复审', description: '详细审核相关材料' },
            { nodeName: '终审', description: '最终审批决定' }
          ],
          approvalHistory: [
            {
              nodeName: '初审',
              approverName: '审核员A',
              approvalTime: '2024-01-15 10:30:00',
              approvalResult: row.approvalStatus === 'rejected' ? '拒绝' : '通过',
              approvalComment: row.approvalStatus === 'rejected' ? '缺少相关检查报告，请补充材料后重新提交' : '初审通过，材料齐全'
            }
          ]
        };

        // 根据状态添加更多审批历史
        if (row.approvalStatus === 'processing' || row.approvalStatus === 'approved') {
          mockFlowData.approvalHistory.push({
            nodeName: '复审',
            approverName: '审核员B',
            approvalTime: '2024-01-15 14:20:00',
            approvalResult: '通过',
            approvalComment: '复审通过，符合上报要求'
          });
        }

        if (row.approvalStatus === 'approved') {
          mockFlowData.approvalHistory.push({
            nodeName: '终审',
            approverName: '主管领导',
            approvalTime: '2024-01-15 16:45:00',
            approvalResult: '通过',
            approvalComment: '终审通过，同意上报'
          });
        }

        this.currentFlowData = {
          workerName: row.workerName,
          reportReason: row.reportReason,
          currentStep: mockFlowData.currentStep,
          approvalSteps: mockFlowData.approvalSteps,
          approvalHistory: mockFlowData.approvalHistory
        };
        this.flowDrawerVisible = true;
      } catch (error) {
        this.$message.error('获取审批流程详情失败');
        console.error(error);
      }
    },

    // 打开审批弹窗
    openApprovalDialog(row) {
      this.currentApprovalData = {
        id: row.id,
        workerName: row.workerName,
        reportReason: row.reportReason,
        currentApprovalNode: row.currentApprovalNode,
        reportTime: row.reportTime
      };

      // 重置表单
      this.approvalForm = {
        result: '',
        comment: ''
      };

      this.approvalDialogVisible = true;

      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.approvalForm) {
          this.$refs.approvalForm.clearValidate();
        }
      });
    },

    // 提交审批
    async submitApproval() {
      try {
        // 表单验证
        const valid = await this.$refs.approvalForm.validate();
        if (!valid) return;

        this.approvalLoading = true;

        // 实际API调用（取消注释以使用真实接口）
        // const res = await submitApprovalResult({
        //   id: this.currentApprovalData.id,
        //   result: this.approvalForm.result,
        //   comment: this.approvalForm.comment
        // });

        // 模拟延迟（实际使用时删除此行）
        await new Promise(resolve => setTimeout(resolve, 1000));

        this.$message.success('审批提交成功');
        this.approvalDialogVisible = false;

        // 刷新列表数据
        this.getData();

      } catch (error) {
        this.$message.error('审批提交失败');
        console.error(error);
      } finally {
        this.approvalLoading = false;
      }
    },

    indexMethod(index) {
      return (this.pageInfo.pageNum - 1) * this.pageInfo.pageSize + (index + 1);
    },

    getApprovalStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'processing': 'primary',
        'approved': 'success',
        'rejected': 'danger'
      };
      return statusMap[status] || 'info';
    },

    getApprovalStatusText(status) {
      const statusMap = {
        'pending': '待审批',
        'processing': '审批中',
        'approved': '已通过',
        'rejected': '已拒绝'
      };
      return statusMap[status] || '未知';
    },

    getApprovalResultType(result) {
      const resultMap = {
        '通过': 'success',
        '拒绝': 'danger',
        '待审批': 'warning'
      };
      return resultMap[result] || 'info';
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  },

  // 监听路由变化
  watch: {
    '$route'(to, from) {
      if (to.name === 'dataReportApproval') {
        this.getData();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.padding {
  padding: 20px;
}

.table {
  margin-top: 15px;
}

.label-title {
  font-size: 14px;
  margin-right: 0.5em;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

::v-deep .el-input {
  width: 250px;
}

::v-deep .el-select {
  width: 250px;
}

::v-deep .el-date-editor.el-input {
  width: 250px;
}

::v-deep .el-date-editor .el-range-input {
  margin-left: 15px;
}

.dataReportApproval {
  position: relative;
}

.first-line {
  display: flex;
  flex-wrap: wrap;

  .label-title {
    text-align: right;
    display: inline-block;
    width: 80px;
    font-size: 14px;
  }

  .search-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 10px;
  }
}

::v-deep .el-tag {
  border-radius: 28px;
}

::v-deep .dataReportApproval .table .el-tag {
  border-radius: 0px;
  text-align: center;
}

// 流程弹窗样式
.flow-content {
  .flow-header {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 16px;
    }
    
    .flow-reason {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }
  
  .flow-steps {
    margin-bottom: 30px;
  }
  
  .flow-details {
    margin-top: 20px;
  }
}

.dialog-footer {
  text-align: center;
}

// 审批弹窗样式
.approval-content {
  .approval-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 16px;
    }

    p {
      margin: 5px 0;
      color: #666;
      font-size: 14px;

      strong {
        color: #333;
      }
    }
  }
}

// 抽屉内容样式
::v-deep .el-drawer__body {
  padding: 0;
}

::v-deep .el-drawer__header {
  padding: 20px 20px 0 20px;
  margin-bottom: 0;
}
</style>
