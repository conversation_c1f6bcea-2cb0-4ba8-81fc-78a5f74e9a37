import request from '@root/publicMethods/request';

// 获取劳动者列表
export function getEmployeeList(data) {
  return request({
    url: '/manage/eHealthRecord/getEmployeeList',
    method: 'post',
    data,
  });
}

export function getEmployeeDetail(params) {
  return request({
    url: '/manage/eHealthRecord/getEmployeeDetail',
    method: 'get',
    params,
  });
}

// 获取劳动者体检报告
export function getEmployeeHCReport(params) {
  return request({
    url: '/manage/eHealthRecord/getEmployeeHCReport',
    method: 'get',
    params,
  });
}

// 企业向劳动者发起授权申请
export function createEHealthRecordAuth(data) {
  return request({
    url: '/manage/eHealthRecord/createEHealthRecordAuth',
    method: 'post',
    data,
  });
}

// 当前企业申请授权列表
export function getEHealthRecordAuthList(data) {
  return request({
    url: '/manage/eHealthRecord/getEHealthRecordAuthList',
    method: 'post',
    data,
  });
}

// 删除授权申请
export function cancelEHealthRecordAuth(params) {
  return request({
    url: '/manage/eHealthRecord/cancelEHealthRecordAuth',
    method: 'get',
    params,
  });
}

export function getContractTableList(params) {
  return request({
    url: '/manage/eHealthRecord/getContractTableList',
    method: 'get',
    params,
  });
}

export function createContract(params) {
  return request({
    url: '/manage/eHealthRecord/addContract',
    method: 'post',
    data: params,
  });
}

export function uploadContract(params) {
  return request({
    url: '/manage/eHealthRecord/uploadContract',
    method: 'post',
    data: params,
  });
}

export function getTjList(params) {
  return request({
    url: '/manage/eHealthRecord/getTjList',
    method: 'get',
    params,
  });
}

export function deleteContract(params) {
  return request({
    url: '/manage/eHealthRecord/deleteContract',
    method: 'post',
    data: params,
  });
}

export function auditContract(params) {
  return request({
    url: '/manage/eHealthRecord/auditContract',
    method: 'post',
    data: params,
  });
}

export function getPersonDetail(params) {
  return request({
    url: '/manage/eHealthRecord/getPersonDetail',
    method: 'get',
    params,
  });
}

export function medicalExamBook(params) {
  return request({
    url: '/manage/eHealthRecord/medicalExamBook',
    method: 'post',
    data: params,
  });
}

// 获取电子健康档案列表
export function getEHealthRecordList(data) {
  return request({
    url: '/manage/eHealthRecord/getEHealthRecordList',
    method: 'post',
    data,
  });
}

// 创建健康档案列表搜索条件
export function createSaveCondition(data) {
  return request({
    url: '/manage/eHealthRecord/createSaveCondition',
    method: 'post',
    data,
  });
}

// 获取健康档案列表搜索条件列表
export function getSaveCondition(data) {
  return request({
    url: '/manage/eHealthRecord/getSaveCondition',
    metho: 'get',
    params: {
      timeQueryType: data,
    },
  });
}

// 删除健康档案列表搜索条件
export function deleteSaveCondition(_id) {
  return request({
    url: '/manage/eHealthRecord/deleteSaveCondition',
    method: 'post',
    data: {
      _id,
    },
  });
}

// 获取体检报告详情
export function getEHealthRecordDetail(params) {
  return request({
    url: '/manage/eHealthRecord/getEHealthRecordDetail',
    method: 'get',
    params,
  });
}

// 获取地区
export function getDistrict(params) {
  return request({
    url: '/manage/eHealthRecord/getDistrict',
    method: 'get',
    params,
  });
}

// 诊断
export function getDiaList(data) {
  // 获取所有单位
  return request({
    url: '/manage/eHealthRecord/getDiaList',
    method: 'post',
    data,
  });
}

// 鉴定
export function getIdentifyList(data) {
  // 获取所有单位
  return request({
    url: '/manage/eHealthRecord/getIdentificationList',
    method: 'post',
    data,
  });
}

// 监管单位查询
export function getSupervisionList(params) {
  return request({
    url: '/manage/eHealthRecord/getSupervisionList',
    method: 'get',
    params,
  });
}

// 提出申诉
export function postComplaint(params) {
  return request({
    url: '/manage/eHealthRecord/postComplaint',
    method: 'get',
    params,
  });
}

// 获取个人基本信息
export function getEHealthRecordBaseInfo(params) {
  return request({
    url: '/manage/eHealthRecord/getEHealthRecordBaseInfo',
    method: 'get',
    params,
  });
}

// 编辑电子健康档案基本信息
export function updateEHealthRecordBaseInfo(data) {
  return request({
    url: '/manage/eHealthRecord/updateEHealthRecordBaseInfo',
    method: 'post',
    data,
  });
}

// 当前单位提出的申诉列表
export function getComplaintListByOrg(data) {
  return request({
    url: '/manage/eHealthRecord/getComplaintListByOrg',
    method: 'post',
    data,
  });
}

// 当前单位待处理的劳动者申诉列表
export function getUserComplaintList(data) {
  return request({
    url: '/manage/eHealthRecord/getUserComplaintList',
    method: 'post',
    data,
  });
}

// 处理劳动者申诉
export function handleComplaint(data) {
  return request({
    url: '/manage/eHealthRecord/handleComplaint',
    method: 'post',
    data,
  });
}

// 获取劳动者职业史
export function getEmploymentHistory(params) {
  return request({
    url: '/manage/eHealthRecord/getEmploymentHistory',
    method: 'get',
    params: {
      params,
    },
  });
}
// 新增一条劳动者职业史
export function addEmploymentHistory(data) {
  return request({
    url: '/manage/eHealthRecord/addEmploymentHistory',
    method: 'post',
    data,
  });
}
// 编辑劳动者职业史
export function editEmploymentHistory(data) {
  return request({
    url: '/manage/eHealthRecord/editEmploymentHistory',
    method: 'post',
    data,
  });
}
// 删除一条劳动者职业史
export function deleteEmploymentHistory(data) {
  return request({
    url: '/manage/eHealthRecord/deleteEmploymentHistory',
    method: 'post',
    data,
  });
}

// 电子健康档案统计分析地区分布
export function getERecordStatisticByArea(data) {
  return request({
    url: '/manage/eHealthRecord/getERecordStatisticByArea',
    method: 'post',
    data,
  });
}

// 电子健康档案统计分析时间分布
export function getERecordStatisticByTime(data) {
  return request({
    url: '/manage/eHealthRecord/getERecordStatisticByTime',
    method: 'post',
    data,
  });
}
// 电子健康档案统计分析范围分布
export function getERecordStatisticByRange(data) {
  return request({
    url: '/manage/eHealthRecord/getERecordStatisticByRange',
    method: 'post',
    data,
  });
}

// 生成劳动者健康档案报表
export function getERecordStatisticExcel(data) {
  return request({
    url: '/manage/eHealthRecord/getERecordStatisticExcel',
    method: 'post',
    data,
  });
}

// 获取行业分类
export function getIndustryCategory(params) {
  return request({
    url: '/api/adminorgGov/getIndustryCategory',
    method: 'get',
    params,
  });
}

// 获取危害因素
export function findHarmFactors(params) {
  return request({
    url: '/manage/eHealthRecord/findHarmFactors',
    method: 'get',
    params,
  });
}

// 获取地址信息
export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}

// 获取数据上报审批列表
export function getDataReportApprovalList(data) {
  return request({
    url: '/manage/eHealthRecord/getDataReportApprovalList',
    method: 'post',
    data,
  });
}

// 获取审批流程详情
export function getApprovalFlowDetail(params) {
  return request({
    url: '/manage/eHealthRecord/getApprovalFlowDetail',
    method: 'get',
    params,
  });
}

// 提交审批结果
export function submitApprovalResult(data) {
  return request({
    url: '/manage/eHealthRecord/submitApprovalResult',
    method: 'post',
    data,
  });
}